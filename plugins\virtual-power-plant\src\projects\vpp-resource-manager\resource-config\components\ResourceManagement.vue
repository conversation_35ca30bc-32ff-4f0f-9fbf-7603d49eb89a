<template>
  <div class="user-management">
    <!-- 摘要卡片区 -->
    <section class="summary-cards">
      <div class="summary-card">
        <div class="summary-content">
          <span class="summary-title">资源（个）</span>
          <span class="summary-value resource-value">
            {{ userStats.resource }}
          </span>
        </div>
      </div>
      <div class="summary-card">
        <div class="summary-content">
          <span class="summary-title">站点（个）</span>
          <span class="summary-value site-value">{{ userStats.site }}</span>
        </div>
      </div>
      <div class="summary-card">
        <div class="summary-content">
          <span class="summary-title">设备（个）</span>
          <span class="summary-value device-value">{{ userStats.device }}</span>
        </div>
      </div>
    </section>
    <!-- 筛选栏 -->
    <section class="filter-bar vpp-filter-bar">
      <el-input
        class="vpp-search-input"
        placeholder="请输入关键字"
        v-model="searchKeyword"
        clearable
        prefix-icon="el-icon-search"
        size="small"
      />
      <div class="vpp-select-group">
        <div class="vpp-select-item">
          <custom-el-select
            v-model="filters.userType"
            placeholder="全部"
            size="small"
            class="w-25"
            clearable
            :prefix_in="$T('资源类型')"
          >
            <el-option
              v-for="item in userTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </custom-el-select>
        </div>

        <div class="vpp-select-item">
          <span class="vpp-select-label">区域</span>
          <el-cascader
            v-model="filters.area"
            :options="regionOptions"
            :placeholder="$T('请选择省/市/区')"
            size="small"
            style="width: 240px"
            clearable
            change-on-select
            :props="{
              checkStrictly: true,
              value: 'code',
              label: 'name',
              children: 'children',
              emitPath: true,
              expandTrigger: 'click'
            }"
          />
        </div>
      </div>
      <div class="vpp-action-buttons">
        <el-button
          type="danger"
          size="small"
          :disabled="selectedResources.length === 0"
          @click="handleBatchDelete"
          plain
        >
          批量删除
        </el-button>
        <el-button type="primary" size="small" @click="handleAdd">
          新增资源
        </el-button>
      </div>
    </section>
    <!-- 表格区域 -->
    <el-table
      class="flex1"
      :data="tableData"
      height="true"
      border
      highlight-current-row
      ref="userTable"
      style="width: 100%"
      v-loading="tableLoading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" width="80" align="center">
        <template slot-scope="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="electric_account" label="电户号" min-width="160" />
      <el-table-column prop="resource_name" label="资源名称" min-width="150" />
      <el-table-column prop="resource_type" label="资源类型" width="120">
        <template slot-scope="scope">
          <span :class="['tag', getResourceTypeClass(scope.row.resource_type)]">
            {{ getResourceTypeText(scope.row.resource_type) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        prop="resource_status"
        label="资源状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span
            :class="[
              'resource-status-tag',
              getResourceStatusClass(scope.row.resource_status)
            ]"
          >
            {{ getResourceStatusText(scope.row.resource_status) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="region" label="区域" width="180" />
      <el-table-column
        prop="rated_capacity"
        label="报装容量（kVA）"
        width="140"
        align="center"
      />
      <el-table-column
        prop="platform_type"
        label="平台直控"
        width="100"
        align="center"
      />
      <el-table-column
        prop="site_capacity"
        label="站点数量（个）"
        width="120"
        align="center"
      />
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <span
            class="action-link detail-link"
            @click="handleDetail(scope.row)"
          >
            详情
          </span>
          <span class="action-link edit-link" @click="handleEdit(scope.row)">
            编辑
          </span>
          <span
            class="action-link delete-link"
            @click="handleDelete(scope.row)"
          >
            删除
          </span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区域 -->
    <div class="table-footer">
      <span>
        共
        <span class="total-highlight">{{ total }}</span>
        个
      </span>
      <el-pagination
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>

    <!-- 资源弹窗（新增/编辑） -->
    <ResourceDialog
      :visible="resourceDialog.visible"
      :mode="resourceDialog.mode"
      :resourceData="resourceDialog.resourceData"
      :province="currentUserProvince"
      :city="currentUserCity"
      @close="onResourceDialogClose"
      @save="onResourceDialogSave"
    />

    <!-- 资源详情抽屉 -->
    <ResourceDetailDrawer
      :visibleTrigger_in="resourceDetailDialog.visibleTrigger"
      :closeTrigger_in="resourceDetailDialog.closeTrigger"
      :inputData_in="resourceDetailDialog.resourceData"
    />
  </div>
</template>

<script>
import ResourceDialog from "./AddResourceDialog.vue";
import ResourceDetailDrawer from "./ResourceDetailDrawer.vue";
import {
  createResource,
  getResourcePage,
  getResourceById,
  updateResource,
  deleteResource
} from "@/api/resource-management";
import { getGeographicalData } from "@/api/base-config";
import { getEnumOptions, getEnumLabel } from "../../../../utils/enumManager";

export default {
  name: "ResourceManagement",
  components: {
    ResourceDialog,
    ResourceDetailDrawer
  },
  props: {
    vppId: {
      type: Number,
      required: true
    },
    userId: {
      type: Number,
      default: null
    },
    statData: {
      type: Object,
      default: () => ({ user: 0, resource: 0, site: 0, device: 0 })
    },
    province: {
      type: Number,
      default: null
    },
    city: {
      type: Number,
      default: null
    },
    node: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      selectedResources: [], // 选中的资源列表
      userStats: {
        resource: 0,
        site: 0,
        device: 0
      },
      filters: {
        userType: "",
        area: []
      },
      searchKeyword: "",
      regionOptions: [],
      geographicalData: null, // 存储地理数据
      userTypeOptions: [],

      tableData: [],

      currentPage: 1,
      pageSize: 10,
      total: 0,
      jumpPage: 1,
      searchTimer: null,
      // 资源弹窗（新增/编辑）
      resourceDialog: {
        visible: false,
        mode: "add", // 'add' 或 'edit'
        resourceData: {}
      },
      // 资源详情抽屉
      resourceDetailDialog: {
        visibleTrigger: 0,
        closeTrigger: 0,
        resourceData: {}
      }
    };
  },
  computed: {
    // 获取当前用户的省份信息
    currentUserProvince() {
      // 如果有直接传入的省份信息，使用它
      if (this.province) {
        return this.province;
      }

      // 如果有用户节点信息，从中获取省份
      if (this.node && this.node.province) {
        return this.node.province;
      }

      return null;
    },

    // 获取当前用户的城市信息
    currentUserCity() {
      // 如果有直接传入的城市信息，使用它
      if (this.city) {
        return this.city;
      }

      // 如果有用户节点信息，从中获取城市
      if (this.node && this.node.city) {
        return this.node.city;
      }

      return null;
    }
  },
  async mounted() {
    // 先加载地理数据，确保区域名称转换正常工作
    await this.loadGeographicalData();

    // 然后加载其他数据
    this.loadResources();
    this.loadStatistics();
    this.loadResourceTypeOptions();
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    "filters.userType": {
      handler() {
        this.currentPage = 1; // 重置页码为第一页
        this.loadResources();
      }
    },
    "filters.area": {
      handler() {
        this.currentPage = 1; // 重置页码为第一页
        this.loadResources();
      }
    },
    statData: {
      handler(newStatData) {
        if (newStatData) {
          // 更新统计数据，但只使用资源、站点、设备数量（用户数不显示）
          this.userStats.resource = newStatData.resource || 0;
          this.userStats.site = newStatData.site || 0;
          this.userStats.device = newStatData.device || 0;
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0) {
          // 存储完整的地理数据用于区域名称转换
          this.geographicalData = response.data;

          // 转换为级联选择器格式（保持三级：省/市/区）
          this.regionOptions = this.transformGeographicalData(
            response.data,
            this.province
          );
        }
      } catch (error) {
        // 加载地理数据失败时静默处理
      }
    },

    // 转换地理数据为级联选择器格式（保持三级：省/市/区）
    transformGeographicalData(data, provinceId) {
      if (!data || !Array.isArray(data)) return [];
      if (provinceId) {
        return data.filter(p => p.code === provinceId);
      }
      return data;
    },

    // 根据province、city、district ID/code获取区域名称
    getRegionName(provinceId, cityId, districtId) {
      if (!this.geographicalData || !Array.isArray(this.geographicalData)) {
        return "--";
      }
      let regionName = "";

      // 查找省份名称
      if (provinceId) {
        const province = this.geographicalData.find(p => p.code === provinceId);
        if (province) {
          regionName = province.name;

          // 查找城市名称
          if (cityId && province.children && Array.isArray(province.children)) {
            const city = province.children.find(c => c.code === cityId);
            if (city) {
              regionName += "/" + city.name;

              // 查找区县名称
              if (districtId && city.children && Array.isArray(city.children)) {
                const district = city.children.find(d => d.code === districtId);
                if (district) {
                  regionName += "/" + district.name;
                }
              }
            }
          }
        }
      }
      return regionName || "--";
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadResources();
      }, 500);
    },

    // 数据转换：将API返回的资源数据转换为表格显示格式
    transformResourceData(apiResource) {
      return {
        id: apiResource.id,
        electric_account: apiResource.electricityUserNumbers || "---",
        resource_name: apiResource.resourceName,
        resource_type: this.getResourceTypeFromApi(apiResource.resourceType),
        resource_status: apiResource.resourceStatus, // 添加资源状态字段映射
        region: this.getRegionName(
          apiResource.province,
          apiResource.city,
          apiResource.district
        ),
        rated_capacity: apiResource.registeredCapacity || 0,
        platform_type: apiResource.platformDirectControl ? "是" : "否",
        site_capacity: apiResource.siteCount || 0,
        // 保留原始API数据用于编辑
        originalData: apiResource
      };
    },

    // 将API资源类型转换为组件使用的类型
    getResourceTypeFromApi(apiType) {
      return apiType;
    },

    // 将表单数据转换为API需要的格式
    transformFormDataToApi(formData) {
      return {
        // 必填字段
        electricityUserNumbers: formData.electricityUserNumbers, // 电户号
        resourceType: formData.resourceType, // 资源类型（1-发电设备，2-储能设备，3-负荷设备，4-微电网资源）
        userId: this.userId, // 所属用户ID（必填）
        vppId: this.vppId, // 所属电厂ID（必填）
        resourceName: formData.resourceName, // 资源名称

        // 地理位置字段（必填字段）
        province: formData.province || null, // 省份
        city: formData.city || null, // 城市（必填）
        district: formData.district || null, // 区域

        // 可选字段
        registeredCapacity: formData.registeredCapacity
          ? parseFloat(formData.registeredCapacity)
          : null, // 报装容量
        responseMode: formData.responseMode || null, // 响应方式(自动/手动)
        electricityUsageCategory: formData.electricityUsageCategory || null, // 资源用电类别
        platformDirectControl: formData.platformDirectControl || null, // 平台直控
        resourceStatus: formData.resourceStatus || null, // 资源状态
        maximumOperablePower: formData.maxRunningPower
          ? parseFloat(formData.maxRunningPower)
          : null, // 最大可运行功率
        minimumOperablePower: formData.minRunningPower
          ? parseFloat(formData.minRunningPower)
          : null, // 最小可运行功率
        maximumUpscalingRate: formData.maxUpRate
          ? parseFloat(formData.maxUpRate)
          : null, // 最大上调速率
        maximumDownwardRate: formData.maxDownRate
          ? parseFloat(formData.maxDownRate)
          : null, // 最大下调速率
        longitude: formData.longitude ? parseFloat(formData.longitude) : null, // 经度
        latitude: formData.latitude ? parseFloat(formData.latitude) : null, // 纬度
        contactPerson: formData.contactPerson || null, // 联系人
        phoneNumber: formData.contactPhone || null, // 联系电话
        address: formData.address || null // 地址
      };
    },

    // 加载资源列表
    async loadResources() {
      try {
        const queryData = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          vppId: this.vppId
        };

        // 添加搜索条件
        if (this.searchKeyword) {
          queryData.name = this.searchKeyword;
        }
        if (this.filters.userType) {
          queryData.type = this.filters.userType;
        }
        if (this.filters.area && this.filters.area.length > 0) {
          const [provinceId, cityId, districtId] = this.filters.area;
          if (provinceId) queryData.provinceId = provinceId;
          if (cityId) queryData.cityId = cityId;
          if (districtId) queryData.districtId = districtId;
        }

        const response = await getResourcePage(queryData);

        if (response.code === 0) {
          this.tableData = response.data.records.map(resource =>
            this.transformResourceData(resource)
          );
          this.total = response.data.total;
          this.currentPage = response.data.pageNum;
          this.pageSize = response.data.pageSize;
        }
      } catch (error) {
        // 加载资源列表失败，静默处理
      }
    },

    // 加载统计数据
    async loadStatistics() {
      // 现在统计数据通过 statData props 传入，不需要 API 调用

      // 如果有传入的统计数据，直接使用
      if (this.statData) {
        this.userStats.resource = this.statData.resource || 0;
        this.userStats.site = this.statData.site || 0;
        this.userStats.device = this.statData.device || 0;
      } else {
        // 兜底：如果没有传入统计数据，保持原有的 API 调用逻辑
        console.warn(
          "⚠️ ResourceManagement - 没有传入统计数据，使用兜底的 API 调用"
        );
        await this.loadStatisticsFromAPI();
      }
    },

    // 兜底方法：从 API 加载统计数据（保持向后兼容）
    async loadStatisticsFromAPI() {
      try {
        // 使用getResourcePage获取当前VPP的所有资源来计算统计
        const response = await getResourcePage({
          pageNum: 1,
          pageSize: 1000, // 使用较大的页面大小来获取所有资源
          vppId: this.vppId
        });

        if (response.code === 0) {
          const resources = response.data.records;
          this.userStats.resource = response.data.total;

          // 计算站点总数
          this.userStats.site = resources.reduce((total, resource) => {
            return total + (resource.site_count || 0);
          }, 0);

          // TODO: 设备数量需要从其他API获取
          // 这里暂时保持为0，后续可以添加相应的API调用
        }
      } catch (error) {
        // 加载统计数据失败，静默处理
      }
    },
    getResourceTypeClass(type) {
      // 根据资源类型返回对应的CSS类
      const typeClassMap = {
        1: "generation-type", // 发电类
        2: "storage-type", // 储能类
        3: "load-type", // 负荷类
        4: "regulation-type" // 调节型
      };
      return typeClassMap[type] || "default-type";
    },
    getResourceTypeText(type) {
      return getEnumLabel("RESOURCE_TYPE", type) || "未知类型";
    },

    // 获取资源状态样式类
    getResourceStatusClass(status) {
      // 根据资源状态返回对应的CSS类
      const statusClassMap = {
        1: "status-operational", // 投运 - 绿色
        2: "status-testing", // 测试 - 蓝色
        3: "status-retired", // 退役 - 橙色
        4: "status-shutdown" // 停机 - 红色
      };
      return statusClassMap[status] || "status-default";
    },

    // 获取资源状态文本
    getResourceStatusText(status) {
      return getEnumLabel("RESOURCE_STATUS_CODES", status) || "未知状态";
    },

    // 加载资源类型选项
    loadResourceTypeOptions() {
      this.userTypeOptions = getEnumOptions("RESOURCE_TYPE");
    },

    handleSelectionChange(selection) {
      this.selectedResources = selection;
    },
    handleBatchDelete() {
      if (this.selectedResources.length === 0) {
        this.$message.warning("请选择要删除的资源");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedResources.length} 个资源吗？`,
        "批量删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(async () => {
          try {
            // 调用后端批量删除接口
            const resourceIds = this.selectedResources.map(
              resource => resource.id
            );

            const response = await deleteResource(resourceIds);
            if (response.code !== 0) {
              this.$message.error(response.msg || "批量删除失败");
              return;
            }

            // 删除成功
            this.$message.success(
              `成功删除 ${this.selectedResources.length} 个资源`
            );

            // 清空选中状态
            this.selectedResources = [];

            // 重新加载数据
            this.loadResources();

            // 通知父组件刷新树形结构
            this.$emit("refresh-tree");
          } catch (error) {
            this.$message.error("批量删除失败，请稍后重试");
          }
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    async handleDetail(row) {
      try {
        // 调用接口获取资源详细信息
        const response = await getResourceById(row.id);
        if (response.code === 0) {
          // 设置详情数据并触发抽屉显示
          this.resourceDetailDialog.resourceData = response.data;
          this.resourceDetailDialog.visibleTrigger = Date.now();
        } else {
          this.$message.error(response.msg || $T("获取资源详情失败"));
        }
      } catch (error) {
        this.$message.error($T("获取资源详情失败，请稍后重试"));
      }
    },
    async handleEdit(row) {
      try {
        // 先调用接口获取资源详细信息
        const response = await getResourceById(row.id);
        if (response.code === 0) {
          // 设置资源数据并显示编辑弹窗
          this.resourceDialog.mode = "edit";
          this.resourceDialog.resourceData = response.data;
          this.resourceDialog.visible = true;
        } else {
          this.$message.error(response.msg || $T("获取资源详情失败"));
        }
      } catch (error) {
        this.$message.error($T("获取资源详情失败，请稍后重试"));
      }
    },
    handleDelete(row) {
      // 弹出删除确认对话框
      this.$confirm(
        $T("确定要删除资源「{0}」吗？删除后将无法恢复。", row.resource_name),
        $T("删除确认"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning",
          closeOnClickModal: false,
          showClose: false,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = $T("删除中...");

              try {
                // 调用后端接口删除资源（传递ID数组）
                const response = await deleteResource([row.id]);
                if (response.code !== 0) {
                  console.error("删除失败:", response.msg);
                  return;
                }

                this.$message.success($T("删除成功"));

                // 刷新表格数据
                this.loadResources();

                // 通知父组件刷新树形结构
                this.$emit("refresh-tree");
              } catch (error) {
                console.error("删除资源失败:", error);
              } finally {
                instance.confirmButtonLoading = false;
              }
            }
            done();
          }
        }
      ).catch(() => {
        this.$message.info($T("已取消删除"));
      });
    },
    handlePageChange(page) {
      console.log("📄 ResourceManagement - 页码变化:", page);
      this.currentPage = page;
      this.loadResources();
    },
    handlePageSizeChange(size) {
      console.log("📄 ResourceManagement - 分页大小变化:", size);
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.loadResources();
    },
    // 新增资源
    handleAdd() {
      this.resourceDialog.mode = "add";
      this.resourceDialog.resourceData = {};
      this.resourceDialog.visible = true;
    },
    // 资源弹窗关闭
    onResourceDialogClose() {
      this.resourceDialog.visible = false;
      this.resourceDialog.resourceData = {};
    },
    // 资源弹窗保存
    async onResourceDialogSave(resourceData) {
      try {
        if (this.resourceDialog.mode === "add") {
          // 新增模式
          // 验证必要的参数
          if (!this.vppId) {
            console.error("缺少VPP ID，无法创建资源");
            return;
          }
          if (!this.userId) {
            console.error("请先选择用户节点，然后再创建资源");
            return;
          }

          // 转换表单数据为API需要的格式
          const apiData = this.transformFormDataToApi(resourceData);

          // 调用后端接口保存资源数据
          const response = await createResource(apiData);

          if (response.code === 0) {
            // 1. 关闭弹窗
            this.resourceDialog.visible = false;

            // 2. 刷新表格数据和统计信息
            await this.loadResources();
            await this.loadStatistics();

            // 3. 通知父组件刷新树形结构
            this.$emit("refresh-tree");

            // 4. 显示成功消息
            this.$message.success($T("资源添加成功"));
          } else {
            console.error("资源添加失败:", response.msg);
          }
        } else {
          // 编辑模式
          // 验证必要的参数
          if (!resourceData.id) {
            console.error("缺少资源ID，无法更新资源");
            return;
          }

          // 转换表单数据为API需要的格式
          const apiData = this.transformFormDataToApi(resourceData);

          // 调用后端接口更新资源数据
          const response = await updateResource(resourceData.id, apiData);

          if (response.code === 0) {
            // 1. 关闭弹窗
            this.resourceDialog.visible = false;

            // 2. 刷新表格数据和统计信息
            await this.loadResources();
            await this.loadStatistics();

            // 3. 通知父组件刷新树形结构
            this.$emit("refresh-tree");

            // 4. 显示成功消息
            this.$message.success($T("资源编辑成功"));
          } else {
            console.error("资源编辑失败:", response.msg);
          }
        }
      } catch (error) {
        const errorMsg =
          this.resourceDialog.mode === "add"
            ? "资源添加失败，请稍后重试"
            : "资源编辑失败，请稍后重试";
        console.error(errorMsg, error);
      }
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style scoped>
.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.summary-cards {
  display: flex;
  gap: var(--J1);
  margin-bottom: var(--J2);
}

.summary-card {
  flex: 1;
  background: var(--BG);
  border-radius: var(--Ra1);
  padding: var(--J3) var(--J4);
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: var(--J0);
}

.summary-title {
  font-size: var(--Ab);
  color: var(--T3);
  font-weight: 400;
  line-height: 22px;
}

.summary-value {
  font-size: var(--Aa);
  font-weight: 500;
  line-height: var(--J2);
  line-height: 22px;
}

.resource-value {
  color: var(--Sta2);
}

.site-value {
  color: var(--SEQ12);
}

.device-value {
  color: var(--SEQ8);
}
.filter-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J2);
  margin-bottom: var(--J2);
}

.vpp-search-input {
  width: 240px;
  height: 32px;
}

.vpp-search-input .el-input__inner {
  height: 32px;
  line-height: 32px;
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.vpp-search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.vpp-search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-search-input .el-input__prefix {
  left: var(--J1);
}

.vpp-search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: 32px;
}
.vpp-select-group {
  display: flex;
  gap: var(--J2);
  flex: 1;
}

.vpp-select-item {
  display: flex;
  align-items: center;
  gap: var(--J1);
  min-width: 120px;
}

.vpp-select-label {
  color: var(--ZS);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  white-space: nowrap;
}

.vpp-select-item .el-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: 32px;
  line-height: 32px;
}

.vpp-select-item .el-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-select-item .el-select .el-input__suffix {
  right: 0;
}

.vpp-select-item .el-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}
.vpp-action-buttons {
  display: flex;
  align-items: center;
}

.vpp-btn-outline {
  height: 32px;
  padding: 0 var(--J2);
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}

.vpp-btn-primary {
  height: 32px;
  padding: 0 var(--J2);
  border: none;
  border-radius: var(--Ra);
  background: var(--ZS);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}
.table-section {
  background: var(--BG);
  border-radius: var(--Ra);
  /* padding: var(--J3); */
}

.table-section .el-table {
  background: transparent;
  border: none;
}

.table-section .el-table th {
  background: var(--BG);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
}

.table-section .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}
.tag {
  display: inline-block;
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 400;
  text-align: center;
}

/* 资源类型标签 */
.tag-generation {
  background: var(--BG4);
  color: var(--F1);
  border: 1px solid var(--F1);
}

.tag-storage {
  background: var(--BG4);
  color: var(--Sta1);
  border: 1px solid var(--Sta1);
}

.tag-load {
  background: var(--BG4);
  color: var(--F2);
  border: 1px solid var(--F2);
}

.tag-microgrid {
  background: var(--BG4);
  color: var(--Sta2);
  border: 1px solid var(--Sta2);
}

.tag-default {
  background: var(--BG);
  color: var(--T3);
  border: 1px solid var(--B1);
}

/* 资源状态标签 */
.resource-status-tag {
  display: inline-block;
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: 1.2;
  border: 1px solid;
  background: var(--BG4);
}

.status-operational {
  color: var(--Sta1);
  border-color: var(--Sta1);
}

.status-testing {
  color: var(--ZS);
  border-color: var(--ZS);
}

.status-retired {
  color: var(--Sta2);
  border-color: var(--Sta2);
}

.status-shutdown {
  color: var(--Sta3);
  border-color: var(--Sta3);
}

.status-default {
  color: var(--T3);
  border-color: var(--B1);
}

.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

.total-highlight {
  color: var(--ZS);
  font-weight: 500;
}

.table-footer .el-pagination {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.table-footer .el-pagination .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  margin: 0;
}

.table-footer .el-pagination .el-pager li.active {
  background: var(--BG2);
  color: var(--T1);
  border-color: var(--B2);
}

.table-footer .el-pagination .btn-prev,
.table-footer .el-pagination .btn-next {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
}

.table-footer .el-pagination .el-pagination__jump {
  margin-left: var(--J2);
  color: var(--T1);
  font-size: var(--Aa);
}

.table-footer .el-pagination .el-pagination__jump .el-input__inner {
  width: 48px;
  height: 32px;
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  text-align: center;
}

/* 批量删除按钮样式 */
.vpp-btn-danger {
  background: var(--Sta3);
  border-color: var(--Sta3);
  color: var(--T5);
}

.vpp-btn-danger:hover {
  background: var(--Sta3);
  border-color: var(--Sta3);
  opacity: 0.8;
}

.vpp-btn-danger:disabled {
  background: var(--B2);
  border-color: var(--B2);
  color: var(--T3);
  cursor: not-allowed;
}

.vpp-btn-danger:disabled:hover {
  background: var(--B2);
  border-color: var(--B2);
  opacity: 1;
}

/* 操作链接样式 */
.action-link {
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}
@media (max-width: 900px) {
  .filter-bar {
    flex-direction: column;
    gap: var(--J1);
    align-items: stretch;
  }

  .vpp-search-input {
    width: 100%;
  }

  .vpp-select-group {
    flex-direction: column;
    gap: var(--J1);
  }

  .vpp-select-item {
    min-width: auto;
    width: 100%;
  }

  .vpp-action-buttons {
    gap: var(--J1);
    flex-wrap: wrap;
  }

  .summary-cards {
    flex-direction: column;
    gap: var(--J1);
  }

  .table-footer {
    flex-direction: column;
    gap: var(--J1);
    align-items: center;
  }
}
.flex1 {
  flex: 1;
  min-width: 0;
  min-height: 0;
}
</style>
