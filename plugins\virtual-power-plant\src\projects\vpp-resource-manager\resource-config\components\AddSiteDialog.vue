<template>
  <div class="add-site-dialog-wrapper">
    <el-dialog
      :title="$T('新增站点')"
      :visible.sync="dialogVisible"
      width="800px"
      @close="handleCancel"
      class="add-site-dialog"
      top="10vh"
    >
      <div class="dialog-content bg-BG1 p-J3">
        <div>
          <el-form
            ref="siteForm"
            :model="formData"
            :rules="formRules"
            label-position="top"
          >
            <!-- 站点名称和站点类型 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('站点名称')" prop="site_name">
                  <el-input
                    v-model="formData.site_name"
                    :placeholder="$T('请输入内容')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('站点类型')" prop="site_type">
                  <el-select
                    v-model="formData.site_type"
                    :placeholder="$T('请选择站点类型')"
                    style="width: 100%"
                    @change="handleSiteTypeChange"
                  >
                    <el-option
                      v-for="item in recommendedSiteTypes"
                      :key="item.value"
                      :label="$T(item.label)"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('经度')" prop="longitude">
                  <el-input
                    v-model="formData.longitude"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('纬度')" prop="latitude">
                  <el-input
                    v-model="formData.latitude"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 电压等级和并网电压 -->
            <el-row :gutter="16" v-if="currentGroup">
              <el-col :span="12">
                <el-form-item
                  :label="$T('电压等级')"
                  prop="voltage_level"
                >
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in voltageLevelOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="currentGroup === 'STORAGE' || currentGroup === 'RENEWABLE'">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="currentGroup === 'OTHER'">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 总装机容量和储电量/发电模式 -->
            <el-row :gutter="16" v-if="currentGroup === 'STORAGE' || currentGroup === 'RENEWABLE'">
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="currentGroup === 'STORAGE'">
                <el-form-item :label="$T('总储电量')" prop="total_storage">
                  <el-input
                    v-model="formData.total_storage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="currentGroup === 'RENEWABLE'">
                <el-form-item :label="$T('发电模式')" prop="generation_mode">
                  <el-select
                    v-model="formData.generation_mode"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in generationModeOptions"
                      :key="item.value"
                      :label="$T(item.label)"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 投运时间和联系人 -->
            <el-row :gutter="16" v-if="currentGroup === 'STORAGE' || currentGroup === 'RENEWABLE'">
              <el-col :span="12">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('联系人')" prop="contact_person">
                  <el-input
                    v-model="formData.contact_person"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 投运时间和关联房间 -->
            <el-row :gutter="16" v-if="currentGroup">
              <el-col :span="12">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('关联房间')" prop="related_room">
                  <el-input
                    v-model="selectedRoomName"
                    :placeholder="$T('请选择')"
                    readonly
                    style="width: 100%"
                  >
                    <el-button
                      slot="suffix"
                      type="text"
                      icon="el-icon-paperclip"
                      @click="openRoomSelector"
                      class="room-selector-btn"
                    />
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 联系电话 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('联系电话')" prop="phone_number">
                  <el-input
                    v-model="formData.phone_number"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 地址 -->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('地址')" prop="site_address">
                  <el-input
                    v-model="formData.site_address"
                    :placeholder="$T('请输入内容')"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 图片 -->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('图片')" prop="images">
                  <el-upload
                    class="image-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="handleImageUpload"
                    accept="image/png,image/jpg,image/jpeg"
                  >
                    <div v-if="formData.imageUrl" class="image-preview">
                      <img
                        :src="getImageDisplayUrl(formData.imageUrl)"
                        class="uploaded-image"
                      />
                      <div class="image-actions">
                        <i
                          class="el-icon-zoom-in"
                          @click.stop="previewImage"
                        ></i>
                        <i class="el-icon-delete" @click.stop="removeImage"></i>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                    </div>
                  </el-upload>
                  <div class="upload-tip text-T3 text-sm mt-J1">
                    {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">
          {{ $T("取消") }}
        </el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          {{ $T("确定") }}
        </el-button>
      </span>
    </el-dialog>

    <RoomSelectorDialog
      :visible.sync="roomSelectorVisible"
      @confirm="handleRoomConfirm"
    />
  </div>
</template>

<script>
import { getEnumOptions, getEnumLabel } from "../../../../utils/enumManager";
import { createSite } from "@/api/site-management";
import { uploadImage, getResourceSiteRelations } from "@/api/base-config";
import RoomSelectorDialog from "./RoomSelectorDialog.vue";
import fileImage from "./FileImage.vue";
export default {
  name: "AddSiteDialog",
  components: {
    RoomSelectorDialog,
    fileImage
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    resourceId: {
      type: [String, Number],
      default: null
    },
    resourceInfo: {
      type: Object,
      default: () => null
    },
    vppId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      siteTypeOptions: [],
      voltageLevelOptions: [], // 电压等级选项
      generationModeOptions: [], // 发电模式选项
      resourceSiteRelations: [], // 资源-站点类型关联关系
      formData: {
        site_type: null,
        site_name: "",
        site_address: "",
        contact_person: "",
        phone_number: "",
        longitude: "",
        latitude: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        operation_date: null,
        related_room: "",
        imageUrl: "",
        generation_mode: "",
        equipment_scale: "",
        service_scope: ""
      },
      formRules: {
        site_name: [
          {
            required: true,
            message: $T("请输入站点名称"),
            trigger: "blur"
          }
        ],
        site_address: [
          {
            required: true,
            message: $T("请输入站点地址"),
            trigger: "blur"
          }
        ],
        contact_person: [
          { required: true, message: $T("请输入联系人"), trigger: "blur" }
        ],
        phone_number: [
          {
            required: true,
            message: $T("请输入联系电话"),
            trigger: "blur"
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: $T("请输入正确的手机号码"),
            trigger: "blur"
          }
        ],
        operation_date: [
          {
            required: true,
            message: $T("请选择投运时间"),
            trigger: "change"
          }
        ],
        voltage_level: [
          {
            required: true,
            message: $T("请选择电压等级"),
            trigger: "change"
          }
        ],
        related_room: [
          {
            required: true,
            message: $T("请选择关联房间"),
            trigger: "change"
          }
        ],
        grid_voltage: [
          {
            required: true,
            message: $T("请输入并网电压"),
            trigger: "blur"
          }
        ],
        total_capacity: [
          {
            required: true,
            message: $T("请输入总装机容量"),
            trigger: "blur"
          }
        ],
        total_storage: [
          {
            required: true,
            message: $T("请输入总储电量"),
            trigger: "blur"
          }
        ],
        generation_mode: [
          {
            required: true,
            message: $T("请选择发电模式"),
            trigger: "change"
          }
        ]
      },
      saving: false,
      roomSelectorVisible: false,
      selectedRoomId: null,
      selectedRoomName: ""
    };
  },
  computed: {
    // 当前站点类型分组
    currentGroup() {
      const group = this.formData.site_type
        ? this.getSiteTypeGroup(this.formData.site_type)
        : null;

      return group;
    },

    // 根据资源类型推荐的站点类型选项
    recommendedSiteTypes() {
      if (!this.resourceInfo || !this.resourceInfo.type) {
        return this.siteTypeOptions;
      }

      // 使用新的推荐函数，基于资源类型编码
      const resourceType = this.resourceInfo.type;
      const recommendedOptions =
        this.getRecommendedSiteTypeOptions(resourceType);

      // 如果没有匹配的推荐类型，返回所有选项
      return recommendedOptions.length > 0
        ? recommendedOptions
        : this.siteTypeOptions;
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal;
      if (newVal) {
        this.resetForm();
        this.setDefaultSiteType();
      }
    },
    // 监听资源信息变化，重新设置默认站点类型
    resourceInfo: {
      handler() {
        if (this.visible) {
          this.setDefaultSiteType();
        }
      },
      deep: true
    }
  },
  async created() {
    this.siteTypeOptions = getEnumOptions("SITE_TYPE_CODES");
    this.voltageLevelOptions = getEnumOptions("VOLTAGE_LEVEL");
    this.generationModeOptions = getEnumOptions("POWER_GENERATION_MODE");
    await this.loadResourceSiteRelations();
  },
  mounted() {
    // 如果对话框已经可见，设置默认站点类型
    if (this.visible) {
      this.setDefaultSiteType();
    }
  },
  methods: {
    // 加载资源-站点类型关联关系
    async loadResourceSiteRelations() {
      try {
        const response = await getResourceSiteRelations();
        if (response.code === 0) {
          this.resourceSiteRelations = response.data || [];
        } else {
          this.$message.error($T("加载站点类型配置失败"));
        }
      } catch (error) {
        this.$message.error($T("加载站点类型配置异常"));
      }
    },

    // 设置默认站点类型
    setDefaultSiteType() {
      this.$nextTick(() => {
        if (
          this.recommendedSiteTypes &&
          this.recommendedSiteTypes.length > 0 &&
          !this.formData.site_type
        ) {
          this.formData.site_type = this.recommendedSiteTypes[0].value;
        }
      });
    },

    // 站点类型变化处理
    handleSiteTypeChange() {
      // 清除之前的验证规则
      this.$nextTick(() => {
        if (this.$refs.siteForm) {
          this.$refs.siteForm.clearValidate();
        }
      });
    },

    // 打开房间选择器
    openRoomSelector() {
      this.roomSelectorVisible = true;
    },

    handleRoomConfirm(selectedRoom) {
      this.selectedRoomId = selectedRoom.id;
      this.selectedRoomName = selectedRoom.name;
      this.formData.related_room = selectedRoom.name;
      this.roomSelectorVisible = false;

      this.$message.success($T("房间选择成功：") + selectedRoom.name);
    },

    // 取消
    handleCancel() {
      this.$emit("close");
    },

    // 保存
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.siteForm.validate();

        this.saving = true;

        // 生成站点编号（格式：SITE + 时间戳后6位 + 随机2位数字）
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 100)
          .toString()
          .padStart(2, "0");
        const siteId = `SITE${timestamp}${random}`;

        // 构建保存数据 - 使用API要求的字段名（驼峰命名）
        const saveData = {
          siteId: siteId,
          resourceId: Number(this.resourceId), // 父组件已经处理过格式，直接转数字
          vppId: Number(this.vppId), // 确保是数字类型
          siteType: Number(this.formData.site_type), // 确保是数字类型
          siteName: this.formData.site_name,
          siteAddress: this.formData.site_address,
          contactPerson: this.formData.contact_person,
          phoneNumber: this.formData.phone_number,
          longitude: this.formData.longitude
            ? Number(this.formData.longitude)
            : null, // 确保是数字类型
          latitude: this.formData.latitude
            ? Number(this.formData.latitude)
            : null // 确保是数字类型
        };

        // 根据站点类型添加特有字段（使用驼峰命名，确保数值字段为数字类型）
        if (this.currentGroup === "STORAGE") {
          saveData.voltageLevel = this.formData.voltage_level
            ? Number(this.formData.voltage_level)
            : null;
          saveData.gridVoltage = this.formData.grid_voltage
            ? Number(this.formData.grid_voltage)
            : null;
          saveData.totalCapacity = this.formData.total_capacity
            ? Number(this.formData.total_capacity)
            : null;
          saveData.totalStorage = this.formData.total_storage
            ? Number(this.formData.total_storage)
            : null;
          saveData.operationDate = this.formData.operation_date
            ? new Date(this.formData.operation_date).getTime()
            : null;
          saveData.roomId = this.selectedRoomId
            ? Number(this.selectedRoomId)
            : null;
          saveData.picturePath = this.formData.imageUrl || null;
        } else if (this.currentGroup === "RENEWABLE") {
          saveData.voltageLevel = this.formData.voltage_level
            ? Number(this.formData.voltage_level)
            : null;
          saveData.generationMode = this.formData.generation_mode
            ? Number(this.formData.generation_mode)
            : null;
          saveData.gridVoltage = this.formData.grid_voltage
            ? Number(this.formData.grid_voltage)
            : null;
          saveData.totalCapacity = this.formData.total_capacity
            ? Number(this.formData.total_capacity)
            : null;
          saveData.operationDate = this.formData.operation_date
            ? new Date(this.formData.operation_date).getTime()
            : null;
          saveData.roomId = this.selectedRoomId
            ? Number(this.selectedRoomId)
            : null;
          saveData.picturePath = this.formData.imageUrl || null;
        } else if (this.currentGroup === "OTHER") {
          saveData.voltageLevel = this.formData.voltage_level
            ? Number(this.formData.voltage_level)
            : null;
          saveData.operationDate = this.formData.operation_date
            ? new Date(this.formData.operation_date).getTime()
            : null;
          saveData.roomId = this.selectedRoomId
            ? Number(this.selectedRoomId)
            : null;
          saveData.picturePath = this.formData.imageUrl || null;
        }

        // 调用API保存站点数据
        const response = await createSite(saveData);

        if (response.code === 0) {
          this.$message.success($T("站点创建成功"));
          this.$emit("save", response.data);
          this.$emit("close");
        } else {
          throw new Error(response.msg || "创建站点失败");
        }
      } catch (error) {
        if (error !== false) {
          // 不是表单验证错误
          this.$message.error($T("保存失败，请重试"));
        }
      } finally {
        this.saving = false;
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        site_type: null,
        site_name: "",
        site_address: "",
        contact_person: "",
        phone_number: "",
        longitude: "",
        latitude: "",
        // 储能类字段
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        operation_date: null,
        related_room: "",
        imageUrl: "",
        // 新能源类字段
        installed_capacity: null,
        annual_generation: null,
        // 其他类字段
        equipment_scale: "",
        service_scope: ""
      };

      // 重置房间选择相关数据
      this.selectedRoomId = null;
      this.selectedRoomName = "";
      this.roomSelectorVisible = false;

      if (this.$refs.siteForm) {
        this.$refs.siteForm.clearValidate();
      }
    },

    // 获取资源类型标签（使用工具函数）
    getResourceTypeName(resourceType) {
      return getEnumLabel("RESOURCE_TYPE", resourceType) || "未知类型";
    },

    // 获取站点类型显示名称
    getSiteTypeDisplayName() {
      if (!this.formData.site_type) return "";
      return this.getSiteTypeName(this.formData.site_type);
    },

    // 处理图片上传
    async handleImageUpload(file) {
      const isValidType = ["image/png", "image/jpg", "image/jpeg"].includes(
        file.type
      );
      const isValidSize = file.size / 1024 / 1024 < 2; // 增加到2MB

      if (!isValidType) {
        this.$message.error($T("只能上传PNG、JPG、JPEG格式的图片"));
        return false;
      }
      if (!isValidSize) {
        this.$message.error($T("图片大小不能超过2MB"));
        return false;
      }

      try {
        // 上传图片

        // 调用上传接口
        const response = await uploadImage(file);

        if (response.code === 0) {
          // 保存图片路径，使用API返回的storedFileName字段
          this.formData.imageUrl = response.data.storedFileName;
          this.$message.success($T("图片上传成功"));
        } else {
          this.$message.error(response.msg || $T("图片上传失败"));
        }
      } catch (error) {
        this.$message.error($T("图片上传失败，请稍后重试"));
      }

      return false; // 阻止自动上传
    },

    // 预览图片
    previewImage() {
      // TODO: 实现图片预览功能
    },

    // 删除图片
    removeImage() {
      this.formData.imageUrl = "";
    },

    // 获取图片显示URL
    getImageDisplayUrl(imagePath) {
      if (!imagePath) return "";

      // 如果是base64数据URL（本地预览），直接返回
      if (imagePath.startsWith("data:")) {
        return imagePath;
      }

      // 如果是服务器路径，构建下载URL
      return `/vpp/api/v1/resource-manager/base-config/images/download/${imagePath}`;
    },

    // 获取站点类型分组
    getSiteTypeGroup(siteType) {
      // 根据站点类型返回对应的分组
      const storageTypes = [2, 10]; // 储能类型：用户侧储能、分布式独立储能
      const renewableTypes = [8, 9]; // 可再生能源类型：分布式光伏、分散式风电
      // 其他类型：自备电源、电动汽车、充电站、换电站、楼宇空调、工商业可调节负荷、非可调节负荷、其他

      if (storageTypes.includes(siteType)) {
        return "STORAGE";
      } else if (renewableTypes.includes(siteType)) {
        return "RENEWABLE";
      } else {
        return "OTHER";
      }
    },

    // 根据资源类型获取推荐的站点类型选项
    getRecommendedSiteTypeOptions(resourceType) {
      // 从动态加载的关联关系中获取站点类型
      const recommendedTypeIds = this.resourceSiteRelations
        .filter(relation => relation.resource_type_id === resourceType)
        .map(relation => relation.site_type_id);

      const filteredOptions = this.siteTypeOptions.filter(option =>
        recommendedTypeIds.includes(option.value)
      );

      return filteredOptions;
    },

    // 获取站点类型名称
    getSiteTypeName(siteType) {
      return getEnumLabel("SITE_TYPE_CODES", siteType);
    }
  }
};
</script>

<style scoped>
/* 包装器样式 - 不影响布局 */
.add-site-dialog-wrapper {
  display: contents;
}

.add-site-dialog .dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.add-site-dialog .section-title {
  font-size: var(--H3);
  border-left: 4px solid var(--ZS);
  padding-left: var(--J1);
  margin-bottom: var(--J2) !important;
}

.add-site-dialog .form-section {
  padding-top: var(--J2);
}

/* 图片上传样式 */
.image-uploader {
  display: inline-block;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: var(--ZS);
}

.upload-placeholder i {
  font-size: 24px;
  color: var(--T3);
}

.image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.image-actions i {
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.3s;
}

.image-actions i:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--T3);
}

/* 房间选择器样式 */
.room-selector-btn {
  padding: 0 8px;
  color: var(--ZS);
  font-size: 16px;
}

.room-selector-btn:hover {
  color: var(--ZS-hover);
}

.room-selector-dialog {
  z-index: 3000 !important;
}

.room-selector-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.room-selector-dialog .el-dialog__body {
  padding: 20px;
}

/* 房间类型选择下拉框样式 */
.room-type-select-dropdown {
  z-index: 4000 !important;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.room-count {
  color: var(--T3);
  font-size: 12px;
  margin-left: 8px;
}

.no-data-tip {
  padding: 12px 20px;
  text-align: center;
  color: var(--T3);
  font-size: 14px;
}

.no-data-tip i {
  margin-right: 4px;
}

.select-tip {
  padding: 8px 12px;
  color: var(--T3);
  font-size: 12px;
  border-bottom: 1px solid var(--BG3);
  margin-bottom: 4px;
  background-color: var(--BG1);
}

.select-tip i {
  margin-right: 4px;
  color: var(--ZS);
}

.room-list {
  border: 1px solid var(--BG3);
  border-radius: 4px;
}
</style>
